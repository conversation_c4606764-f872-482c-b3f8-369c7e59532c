# Environment Setup Guide

This guide explains how to set up and run the mobile app with different environment configurations.

## Overview

The mobile app supports multiple environments:
- **Development** (`.env.development`)
- **Staging** (`.env.staging`) 
- **Production** (`.env.production`)

Each environment has its own configuration file with specific API URLs, debug settings, and feature flags.

## Environment Files

### `.env.development`
Used for local development with your backend running on localhost or local network.

```env
# Development Environment Configuration
API_URL=http://localhost:3002/
WS_URL=ws://localhost:3002/
ENV=development
DEBUG=true
ENABLE_DEBUG_LOGS=true
ENABLE_REACTOTRON=true
ENABLE_DEV_MENU=true
APP_NAME=Gupt Messenger (Dev)
APP_VERSION=1.0.0-dev
```

### `.env.staging`
Used for testing with staging backend servers.

```env
# Staging Environment Configuration
API_URL=https://chatspot-backend-staging.onrender.com/
WS_URL=wss://chatspot-backend-staging.onrender.com/
ENV=staging
DEBUG=true
ENABLE_DEBUG_LOGS=true
ENABLE_REACTOTRON=false
ENABLE_DEV_MENU=true
APP_NAME=Gupt Messenger (Staging)
APP_VERSION=1.0.0-staging
```

### `.env.production`
Used for production builds with live backend servers.

```env
# Production Environment Configuration
API_URL=https://tough-disc-merit-median.trycloudflare.com/
WS_URL=wss://tough-disc-merit-median.trycloudflare.com/
ENV=production
DEBUG=false
ENABLE_DEBUG_LOGS=false
ENABLE_REACTOTRON=false
ENABLE_DEV_MENU=false
APP_NAME=Gupt Messenger
APP_VERSION=1.0.0
```

## Running with Specific Environments

### Development Environment

```bash
# Start Metro bundler for development
yarn dev

# Run on Android with development config
yarn dev:android

# Run on iOS with development config
yarn dev:ios
```

### Staging Environment

```bash
# Start Metro bundler for staging
yarn staging

# Run on Android with staging config
yarn staging:android

# Run on iOS with staging config
yarn staging:ios
```

### Production Environment

```bash
# Start Metro bundler for production
yarn prod

# Run on Android with production config
yarn prod:android

# Run on iOS with production config
yarn prod:ios
```

## Environment Validation

Validate your environment configuration:

```bash
# Check if environment variables are properly set
yarn env:validate
```

This will:
- ✅ Verify all required environment variables are set
- ✅ Display current environment configuration
- ❌ Show errors if configuration is invalid

## Environment Variables Reference

| Variable | Description | Example |
|----------|-------------|---------|
| `API_URL` | Backend API endpoint | `http://localhost:3002/` |
| `WS_URL` | WebSocket endpoint | `ws://localhost:3002/` |
| `ENV` | Environment name | `development` |
| `DEBUG` | Enable debug mode | `true` |
| `ENABLE_DEBUG_LOGS` | Enable debug logging | `true` |
| `ENABLE_REACTOTRON` | Enable Reactotron debugging | `true` |
| `ENABLE_DEV_MENU` | Enable development menu | `true` |
| `APP_NAME` | Application display name | `Gupt Messenger (Dev)` |
| `APP_VERSION` | Application version | `1.0.0-dev` |

## Creating Custom Environment Files

1. **Copy the example file:**
   ```bash
   cp .env.example .env.custom
   ```

2. **Edit the custom file:**
   ```env
   API_URL=http://your-custom-backend.com/
   WS_URL=ws://your-custom-backend.com/
   ENV=development
   DEBUG=true
   # ... other variables
   ```

3. **Run with custom environment:**
   ```bash
   NODE_ENV=custom yarn start
   NODE_ENV=custom yarn android
   ```

## Troubleshooting

### Environment Variables Not Loading

1. **Clear Metro cache:**
   ```bash
   yarn start --reset-cache
   ```

2. **Verify babel configuration:**
   Check that `babel.config.js` includes the `react-native-dotenv` plugin.

3. **Check file naming:**
   Ensure environment files follow the pattern `.env.{NODE_ENV}`

### Invalid Environment Configuration

1. **Run validation:**
   ```bash
   yarn env:validate
   ```

2. **Check for missing variables:**
   Ensure all required variables are defined in your `.env` file.

3. **Verify variable types:**
   Boolean variables should be strings: `"true"` or `"false"`

### Network Connection Issues

1. **For Android Emulator:**
   ```bash
   # Forward localhost ports
   adb reverse tcp:3002 tcp:3002
   ```

2. **For Physical Devices:**
   Update `LOCAL_IP` in your `.env` file to your computer's IP address.

3. **Check backend status:**
   Ensure your backend server is running and accessible.

## Best Practices

1. **Never commit sensitive data:**
   - Add `.env.local` to `.gitignore`
   - Use `.env.example` as a template

2. **Use environment-specific configurations:**
   - Different API URLs for each environment
   - Disable debug features in production
   - Use descriptive app names for each environment

3. **Validate before deployment:**
   - Always run `yarn env:validate` before building
   - Test environment switching locally

4. **Keep configurations in sync:**
   - Update all environment files when adding new variables
   - Document new variables in this guide

## Next Steps

After setting up your environment:

1. **Test API connectivity:** Verify the app can connect to your backend
2. **Test WebSocket connections:** Ensure real-time features work
3. **Verify push notifications:** Test FCM integration if configured
4. **Check media uploads:** Test file upload functionality
