/**
 * Environment variables utility for React Native
 *
 * This file provides type-safe access to environment variables
 * and ensures they are properly loaded from .env files.
 *
 * Note: Firebase configuration is NOT included here because @react-native-firebase
 * automatically reads configuration from Google Services files:
 * - Android: android/app/google-services.json
 * - iOS: ios/mobile/GoogleService-Info.plist
 */

import {
  API_URL,
  WS_URL,
  ENV,
  DEBUG,
  BACKEND_PORT,
  LOCAL_IP,
  LOG_LEVEL,
  ENABLE_LOGGING,
  ENABLE_DEBUG_LOGS,
  ENABLE_REACTOTRON,
  ENABLE_DEV_MENU,
  APP_NAME,
  APP_VERSION,
} from '@env';

// Define the shape of our environment variables
interface EnvVariables {
  API_URL: string;
  WS_URL: string;
  ENV: 'development' | 'staging' | 'production';
  DEBUG: boolean;
  BACKEND_PORT: string;
  LOCAL_IP: string;
  LOG_LEVEL: string;
  ENABLE_LOGGING: boolean;
  ENABLE_DEBUG_LOGS: boolean;
  ENABLE_REACTOTRON: boolean;
  ENABLE_DEV_MENU: boolean;
  APP_NAME: string;
  APP_VERSION: string;
}

// Get environment variables with type safety
// Development mode (__DEV__) uses .env file values
// Production mode uses cloudflare URLs
export const env: EnvVariables = {
  API_URL: __DEV__ ? API_URL : 'https://tough-disc-merit-median.trycloudflare.com/',
  WS_URL: __DEV__ ? WS_URL : 'wss://tough-disc-merit-median.trycloudflare.com/',
  ENV: (ENV as 'development' | 'staging' | 'production') || (__DEV__ ? 'development' : 'production'),
  DEBUG: DEBUG === 'true' || __DEV__,
  BACKEND_PORT: BACKEND_PORT || '3002',
  LOCAL_IP: LOCAL_IP || '***********',
  LOG_LEVEL: LOG_LEVEL || 'debug',
  ENABLE_LOGGING: ENABLE_LOGGING === 'true' || __DEV__,
  ENABLE_DEBUG_LOGS: ENABLE_DEBUG_LOGS === 'true' || __DEV__,
  ENABLE_REACTOTRON: ENABLE_REACTOTRON === 'true' || __DEV__,
  ENABLE_DEV_MENU: ENABLE_DEV_MENU === 'true' || __DEV__,
  APP_NAME: APP_NAME || 'Gupt Messenger',
  APP_VERSION: APP_VERSION || '1.0.0',
};

// Helper functions
export const isDevelopment = (): boolean => env.ENV === 'development';
export const isStaging = (): boolean => env.ENV === 'staging';
export const isProduction = (): boolean => env.ENV === 'production';
export const isDebugMode = (): boolean => env.DEBUG;

// API URL helpers
export const getApiUrl = (): string => env.API_URL;
export const getWsUrl = (): string => env.WS_URL;

// Conditional logging that only works in development/debug mode
export const debugLog = (...args: any[]): void => {
  if (isDebugMode()) {
    console.log('[DEBUG]', ...args);
  }
};

// Additional helper functions
export const isReactotronEnabled = (): boolean => env.ENABLE_REACTOTRON;
export const isDevMenuEnabled = (): boolean => env.ENABLE_DEV_MENU;
export const isDebugLogsEnabled = (): boolean => env.ENABLE_DEBUG_LOGS;

// Environment validation
export const validateEnvironment = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!env.API_URL) {
    errors.push('API_URL is not defined');
  }

  if (!env.WS_URL) {
    errors.push('WS_URL is not defined');
  }

  if (!['development', 'staging', 'production'].includes(env.ENV)) {
    errors.push('ENV must be one of: development, staging, production');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Log current environment configuration (for debugging)
export const logEnvironmentInfo = (): void => {
  if (isDebugMode()) {
    console.log('🌍 Environment Configuration:');
    console.log('  ENV:', env.ENV);
    console.log('  API_URL:', env.API_URL);
    console.log('  WS_URL:', env.WS_URL);
    console.log('  DEBUG:', env.DEBUG);
    console.log('  APP_NAME:', env.APP_NAME);
    console.log('  APP_VERSION:', env.APP_VERSION);
    console.log('  ENABLE_LOGGING:', env.ENABLE_LOGGING);
    console.log('  ENABLE_REACTOTRON:', env.ENABLE_REACTOTRON);
    console.log('  ENABLE_DEV_MENU:', env.ENABLE_DEV_MENU);
  }
};

export default env;
