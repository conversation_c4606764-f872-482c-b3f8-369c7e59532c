#!/usr/bin/env node

/**
 * Environment Helper Script
 * 
 * This script helps manage environment configurations for the mobile app.
 * It can validate, display, and switch between different environments.
 */

const fs = require('fs');
const path = require('path');

// Available environments
const ENVIRONMENTS = ['development', 'staging', 'production'];

// Required environment variables
const REQUIRED_VARS = [
  'API_URL',
  'WS_URL',
  'ENV',
  'DEBUG',
];

// Optional environment variables with defaults
const OPTIONAL_VARS = {
  'BACKEND_PORT': '3002',
  'LOCAL_IP': '***********',
  'LOG_LEVEL': 'debug',
  'ENABLE_LOGGING': 'true',
  'ENABLE_DEBUG_LOGS': 'true',
  'ENABLE_REACTOTRON': 'true',
  'ENABLE_DEV_MENU': 'true',
  'APP_NAME': 'Gupt Messenger',
  'APP_VERSION': '1.0.0',
};

/**
 * Parse environment file
 */
function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return env;
}

/**
 * Validate environment configuration
 */
function validateEnvironment(env) {
  const errors = [];
  const warnings = [];
  
  // Check required variables
  REQUIRED_VARS.forEach(varName => {
    if (!env[varName]) {
      errors.push(`Missing required variable: ${varName}`);
    }
  });
  
  // Check optional variables
  Object.keys(OPTIONAL_VARS).forEach(varName => {
    if (!env[varName]) {
      warnings.push(`Missing optional variable: ${varName} (will use default: ${OPTIONAL_VARS[varName]})`);
    }
  });
  
  // Validate ENV value
  if (env.ENV && !ENVIRONMENTS.includes(env.ENV)) {
    errors.push(`Invalid ENV value: ${env.ENV}. Must be one of: ${ENVIRONMENTS.join(', ')}`);
  }
  
  // Validate URLs
  if (env.API_URL && !env.API_URL.match(/^https?:\/\/.+/)) {
    errors.push(`Invalid API_URL format: ${env.API_URL}`);
  }
  
  if (env.WS_URL && !env.WS_URL.match(/^wss?:\/\/.+/)) {
    errors.push(`Invalid WS_URL format: ${env.WS_URL}`);
  }
  
  return { errors, warnings };
}

/**
 * Display environment information
 */
function displayEnvironment(envName, env) {
  console.log(`\n🌍 Environment: ${envName.toUpperCase()}`);
  console.log('━'.repeat(50));
  
  if (!env) {
    console.log('❌ Environment file not found');
    return;
  }
  
  // Display configuration
  Object.keys(env).sort().forEach(key => {
    const value = env[key];
    const isUrl = key.includes('URL');
    const isSecret = key.toLowerCase().includes('key') || key.toLowerCase().includes('secret');
    
    if (isSecret) {
      console.log(`  ${key}: ${'*'.repeat(value.length)}`);
    } else if (isUrl) {
      console.log(`  ${key}: ${value}`);
    } else {
      console.log(`  ${key}: ${value}`);
    }
  });
  
  // Validate and show results
  const validation = validateEnvironment(env);
  
  if (validation.errors.length > 0) {
    console.log('\n❌ Validation Errors:');
    validation.errors.forEach(error => console.log(`  • ${error}`));
  }
  
  if (validation.warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    validation.warnings.forEach(warning => console.log(`  • ${warning}`));
  }
  
  if (validation.errors.length === 0) {
    console.log('\n✅ Environment configuration is valid');
  }
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'list':
      console.log('📋 Available environments:');
      ENVIRONMENTS.forEach(env => {
        const filePath = path.join(__dirname, '..', `.env.${env}`);
        const exists = fs.existsSync(filePath);
        console.log(`  • ${env} ${exists ? '✅' : '❌'}`);
      });
      break;
      
    case 'show':
      const envName = args[1] || process.env.NODE_ENV || 'development';
      const envPath = path.join(__dirname, '..', `.env.${envName}`);
      const env = parseEnvFile(envPath);
      displayEnvironment(envName, env);
      break;
      
    case 'validate':
      const validateEnv = args[1] || process.env.NODE_ENV || 'development';
      const validatePath = path.join(__dirname, '..', `.env.${validateEnv}`);
      const validateConfig = parseEnvFile(validatePath);
      
      if (!validateConfig) {
        console.log(`❌ Environment file not found: .env.${validateEnv}`);
        process.exit(1);
      }
      
      const validation = validateEnvironment(validateConfig);
      
      if (validation.errors.length > 0) {
        console.log('❌ Validation failed:');
        validation.errors.forEach(error => console.log(`  • ${error}`));
        process.exit(1);
      } else {
        console.log('✅ Environment validation passed');
      }
      break;
      
    case 'help':
    default:
      console.log('🔧 Environment Helper');
      console.log('\nUsage:');
      console.log('  node scripts/env-helper.js <command> [options]');
      console.log('\nCommands:');
      console.log('  list                    List all available environments');
      console.log('  show [environment]      Show environment configuration');
      console.log('  validate [environment]  Validate environment configuration');
      console.log('  help                    Show this help message');
      console.log('\nExamples:');
      console.log('  node scripts/env-helper.js list');
      console.log('  node scripts/env-helper.js show development');
      console.log('  node scripts/env-helper.js validate staging');
      break;
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  parseEnvFile,
  validateEnvironment,
  displayEnvironment,
};
