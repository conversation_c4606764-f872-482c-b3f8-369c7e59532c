# Quick Start Guide - Environment Setup

## TL;DR - How to Run with Specific .env Files

### 🚀 Quick Commands

```bash
# Development (uses .env.development)
yarn dev:android    # Android
yarn dev:ios        # iOS

# Staging (uses .env.staging)
yarn staging:android    # Android
yarn staging:ios        # iOS

# Production (uses .env.production)
yarn prod:android    # Android
yarn prod:ios        # iOS
```

### 🔍 Environment Management

```bash
# List available environments
yarn env:list

# Show current environment config
yarn env:show

# Validate environment
yarn env:validate

# Show specific environment
yarn env:show development
yarn env:show staging
yarn env:show production
```

## Environment Files Overview

| File | Purpose | API URL |
|------|---------|---------|
| `.env.development` | Local development | `http://localhost:3002/` |
| `.env.staging` | Testing/staging | `https://chatspot-backend-staging.onrender.com/` |
| `.env.production` | Production builds | `https://tough-disc-merit-median.trycloudflare.com/` |

## Step-by-Step Setup

### 1. Choose Your Environment

**For local development:**
```bash
# Make sure your backend is running on localhost:3002
yarn dev:android
```

**For testing with staging server:**
```bash
# Uses remote staging server
yarn staging:android
```

**For production testing:**
```bash
# Uses production server
yarn prod:android
```

### 2. Verify Environment

```bash
# Check if your environment is configured correctly
yarn env:validate

# See what environment variables are loaded
yarn env:show
```

### 3. Run the App

```bash
# Start Metro bundler (in one terminal)
yarn dev  # or yarn staging, yarn prod

# Run the app (in another terminal)
yarn dev:android  # or staging:android, prod:android
```

## Common Issues & Solutions

### ❌ Environment variables not loading
```bash
# Clear cache and restart
yarn start --reset-cache
```

### ❌ Can't connect to backend
```bash
# Check your environment
yarn env:show

# For Android emulator with localhost
adb reverse tcp:3002 tcp:3002
```

### ❌ Wrong environment loaded
```bash
# Make sure you're using the right command
yarn dev:android      # Uses .env.development
yarn staging:android  # Uses .env.staging
yarn prod:android     # Uses .env.production
```

## Environment Variables Explained

### Required Variables
- `API_URL` - Backend API endpoint
- `WS_URL` - WebSocket endpoint  
- `ENV` - Environment name (development/staging/production)
- `DEBUG` - Enable debug mode (true/false)

### Optional Variables
- `APP_NAME` - App display name
- `APP_VERSION` - App version
- `ENABLE_DEBUG_LOGS` - Enable debug logging
- `ENABLE_REACTOTRON` - Enable Reactotron debugging
- `ENABLE_DEV_MENU` - Enable development menu

## Creating Custom Environment

1. **Copy existing environment:**
   ```bash
   cp .env.development .env.custom
   ```

2. **Edit the file:**
   ```env
   API_URL=http://your-backend.com/
   WS_URL=ws://your-backend.com/
   ENV=development
   DEBUG=true
   ```

3. **Run with custom environment:**
   ```bash
   NODE_ENV=custom yarn start
   NODE_ENV=custom yarn android
   ```

## Pro Tips

- 🔍 Always run `yarn env:validate` before building
- 🚀 Use `yarn env:show` to debug environment issues
- 📱 Different environments can have different app names
- 🔧 Clear Metro cache if environment changes don't take effect
- 🌐 Use `adb reverse` for Android emulator with localhost

## Need Help?

1. **Check environment:** `yarn env:show`
2. **Validate config:** `yarn env:validate`
3. **List environments:** `yarn env:list`
4. **Get help:** `yarn env:help`

For detailed setup instructions, see `ENVIRONMENT_SETUP.md`.
