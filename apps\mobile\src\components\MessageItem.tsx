import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { radius, spacing, typography, useTheme, shadows } from '../theme';
import ImageViewer from './MediaViewer';
import { useDispatch, useSelector } from 'react-redux';
import { downloadMediaRequest } from '../redux/sagas/mediaSaga';
import { selectMediaDownloading, selectMediaDownloadProgress } from '../redux/slices/mediaSlice';

interface MessageItemProps {
  message?: any;
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

const { width: screenWidth } = Dimensions.get('window');

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [showMediaViewer, setShowMediaViewer] = useState(false);
  const dispatch = useDispatch();

  // Media download state
  const isDownloading = useSelector((state: any) =>
    selectMediaDownloading(state, message?.media_id || ''));
  const downloadProgress = useSelector((state: any) =>
    selectMediaDownloadProgress(state, message?.media_id || ''));



  if (!message) return null;

  if (message.type === 'clear_chat') {
    return (
      <View style={styles.systemMessageContainer}>
        <View style={[
          styles.systemMessageContent,
          message.status === 'sending' && styles.sendingGradient
        ]}>
          <Text style={styles.systemMessageText}>
            Chat cleared by {message.is_mine ? 'you' : (message.sender_username || 'Unknown User')}
          </Text>
          <Text style={styles.messageTime}>{formatTime(message.timestamp)}</Text>
        </View>
      </View>
    );
  }

  if (message.type === 'typing') return null;

  const handleDownloadMedia = () => {
    if (message?.media_id && message?.id) {
      dispatch(downloadMediaRequest(message.media_id, message.id));
    }
  };

  const renderImageContent = () => {
    if (message.type !== 'media' || !message.media_type?.startsWith('image/')) {
      return null;
    }

    // Check if media is downloaded locally
    const hasLocalMedia = message.media_local_path;
    const imageSource = hasLocalMedia
      ? { uri: `file://${message.media_local_path}` }
      : message.media_url
        ? { uri: message.media_url }
        : null;

    if (!imageSource && !message.media_id) {
      return null;
    }

    return (
      <View style={styles.imageContainer}>
        {imageSource ? (
          <TouchableOpacity onPress={() => setShowMediaViewer(true)}>
            <Image
              source={imageSource}
              style={styles.imageMedia}
              resizeMode="cover"
            />
          </TouchableOpacity>
        ) : (
          // Show download button if no local media and no direct URL
          <View style={styles.mediaPlaceholder}>
            <TouchableOpacity
              style={styles.downloadButton}
              onPress={handleDownloadMedia}
              disabled={isDownloading}
            >
              {isDownloading ? (
                <View style={styles.downloadingContainer}>
                  <ActivityIndicator size="small" color={colors.primary} />
                  <Text style={styles.downloadingText}>
                    {downloadProgress}%
                  </Text>
                </View>
              ) : (
                <View style={styles.downloadContainer}>
                  <Text style={styles.downloadIcon}>⬇</Text>
                  <Text style={styles.downloadText}>Download</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        )}

        {message.media_filename && (
          <Text style={styles.imageFilename} numberOfLines={1}>
            {message.media_filename}
          </Text>
        )}
      </View>
    );
  };

  const renderStatusIcon = () => {
    if (!message.isMine && !message.is_mine) return null;

    switch (message.status) {
      case 'sending':
        return (
          <View style={styles.statusContainer}>
            <ActivityIndicator size="small" color={colors.myMessageTick} />
          </View>
        );
      case 'sent':
        return (
          <View style={styles.statusContainer}>
            <Text style={[styles.singleTick, { color: colors.myMessageTick }]}>✓</Text>
          </View>
        );
      case 'delivered':
      case 'read':
        return (
          <View style={styles.statusContainer}>
            <Text style={[styles.doubleTick, { color: colors.success }]}>✓✓</Text>
          </View>
        );
      default:
        return null;
    }
  };

  const isMine = message.isMine || message.is_mine;

  return (
    <View style={[
      styles.messageContainer,
      isMine ? styles.sentMessage : styles.receivedMessage,
      isLastInGroup && styles.lastInGroup
    ]}>
      <View style={[
        styles.messageContent,
        isMine ? styles.sentContent : styles.receivedContent,
        message.status === 'sending' && styles.sendingGradient
      ]}>
        {/* Render image content if it's a media message */}
        {message.type === 'media' && renderImageContent()}

        {/* Render text content if present */}
        {message.message && (
          <Text style={[
            styles.messageText,
            isMine ? styles.sentText : styles.receivedText,
            message.type === 'media' && styles.mediaCaption
          ]}>
            {message.message}
          </Text>
        )}

        <View style={styles.messageInfo}>
          <Text style={[
            styles.messageTime,
            { color: isMine ? colors.myMessageTime : colors.peerMessageTime }
          ]}>
            {formatTime(message.timestamp)}
          </Text>
          {renderStatusIcon()}
        </View>
      </View>

      {/* Image Viewer Modal */}
      {showMediaViewer && message.type === 'media' && message.media_type?.startsWith('image/') && (
        <ImageViewer
          visible={showMediaViewer}
          imageUrl={message.media_local_path
            ? `file://${message.media_local_path}`
            : message.media_url}
          imageFilename={message.media_filename}
          onClose={() => setShowMediaViewer(false)}
        />
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  messageContainer: {
    marginVertical: 3,
    marginHorizontal: spacing.md,
  },
  sentMessage: {
    alignItems: 'flex-end',
  },
  receivedMessage: {
    alignItems: 'flex-start',
  },
  lastInGroup: {
    marginBottom: spacing.sm,
  },
  messageContent: {
  maxWidth: '80%',
  borderRadius: radius.md,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
},
  sentContent: {
    backgroundColor: colors.primaryLight3, // Use new message-specific colors
      // ✅ Cross-platform shadow
  shadowColor: colors.primaryColor,
  shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 3, // for Android only
  },
  receivedContent: {
    backgroundColor: colors.toneLight3, // Use new message-specific colors
    shadowColor: colors.toneColor,
  shadowOffset: { width: 0, height: 0 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 3, // for Android only
  },
  sendingGradient: {
    opacity: 0.7,
  },
  messageText: {
    fontSize: 16,
    fontFamily: 'Outfit-Regular',
    lineHeight: 20,
  },
  sentText: {
    color: colors.myMessageText, // Use new message-specific colors
  },
  receivedText: {
    color: colors.peerMessageText, // Use new message-specific colors
  },
  messageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  messageTime: {
    marginRight: spacing.xs,
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    lineHeight: 16,
    color:colors.toneLight3,
  },
  statusContainer: {
    marginLeft: spacing.xs,
  },
  singleTick: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  doubleTick: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  systemMessageContainer: {
    alignItems: 'center',
    marginVertical: spacing.sm,
  },
  systemMessageContent: {
    backgroundColor: colors.gray100,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    alignItems: 'center',
  },
  systemMessageText: {
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
    textAlign: 'center',
  },
  // Image styles
  imageContainer: {
    marginBottom: spacing.xs,
  },
  imageMedia: {
    width: screenWidth * 0.6,
    height: screenWidth * 0.6 * 0.75, // 4:3 aspect ratio
    borderRadius: radius.sm,
  },
  imageFilename: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  mediaCaption: {
    marginTop: spacing.xs,
    fontSize: 14,
  },
  // Download styles
  mediaPlaceholder: {
    width: screenWidth * 0.6,
    height: screenWidth * 0.6 * 0.75,
    backgroundColor: colors.gray100,
    borderRadius: radius.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  downloadButton: {
    padding: spacing.md,
    borderRadius: radius.sm,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  downloadContainer: {
    alignItems: 'center',
  },
  downloadIcon: {
    fontSize: 24,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  downloadText: {
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
    color: colors.white,
  },
  downloadingContainer: {
    alignItems: 'center',
  },
  downloadingText: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    color: colors.primary,
    marginTop: spacing.xs,
  },
});

export default MessageItem;
