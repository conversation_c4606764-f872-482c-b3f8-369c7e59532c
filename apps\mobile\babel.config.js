module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    ['@babel/plugin-proposal-decorators', { legacy: true }],
    [
      'module:react-native-dotenv',
      {
        moduleName: '@env',
        path: '.env',
        blacklist: null,
        whitelist: null,
        safe: false,
        allowUndefined: true,
        // Support multiple environment files
        envName: 'NODE_ENV',
        // Load environment-specific files
        paths: [
          '.env',
          '.env.local',
          `.env.${process.env.NODE_ENV || 'development'}`,
          `.env.${process.env.NODE_ENV || 'development'}.local`,
        ],
      },
    ],
  ],
};
