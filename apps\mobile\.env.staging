# Staging Environment Configuration
# This file is automatically loaded when NODE_ENV=staging

# Staging Backend Configuration
API_URL=https://chatspot-backend-staging.onrender.com/
WS_URL=wss://chatspot-backend-staging.onrender.com/

# Environment Settings
ENV=staging
DEBUG=true

# Staging Features
ENABLE_DEBUG_LOGS=true
ENABLE_REACTOTRON=false
ENABLE_DEV_MENU=true

# App Configuration
APP_NAME=Gupt Messenger (Staging)
APP_VERSION=1.0.0-staging

# Backend Configuration (fallback values)
BACKEND_PORT=3002
LOCAL_IP=***********

# Logging
LOG_LEVEL=debug
ENABLE_LOGGING=true
