import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '../context/ThemeContext';
import { spacing, radius } from '../theme';
import EmojiBar from './EmojiBar';
import { sendMessageRequest } from '../redux/slices/socketSlice';
import { selectConnected, selectError } from '../redux/slices/socketSlice';
import { selectAuthToken } from '../redux/slices/authSlice';
import { selectEmojiReactionsEnabled } from '../redux/slices/emojiReactionSlice';
import { uploadMediaRequest } from '../redux/sagas/mediaSaga';
import { clearMediaState } from '../redux/slices/mediaSlice';
import {
  selectMediaUploading,
  selectMediaUploadProgress,
  selectUploadedMedia,
  selectMediaError
} from '../redux/slices/mediaSlice';
import MediaPicker, { ImageFile } from './MediaPicker';

interface MessageInputProps {
  receiverUsername: string;
  onFocus?: () => void;
  onBlur?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({
  receiverUsername,
  onFocus,
  onBlur
}) => {
  const dispatch = useDispatch();
  const connected = useSelector(selectConnected);
  const error = useSelector(selectError);
  const emojiReactionsEnabled = useSelector(selectEmojiReactionsEnabled);
  const authToken = useSelector(selectAuthToken);
  const { colors } = useTheme();

  // Media upload state from Redux
  const isUploadingMedia = useSelector(selectMediaUploading);
  const uploadProgress = useSelector(selectMediaUploadProgress);
  const uploadedMedia = useSelector(selectUploadedMedia);
  const mediaError = useSelector(selectMediaError);

  const [message, setMessage] = useState<string>('');
  const [sendStatus, setSendStatus] = useState<{ success: boolean, message: string } | null>(null);
  const [showMediaPicker, setShowMediaPicker] = useState(false);
  const inputRef = useRef<TextInput>(null);

  // Watch for errors from Redux
  useEffect(() => {
    if (error && error.includes('Failed to send message')) {
      setSendStatus({
        success: false,
        message: error
      });
    }
  }, [error]);

  // Watch for media upload success (this is now handled in socketSaga)
  useEffect(() => {
    if (uploadedMedia) {
      setSendStatus({
        success: true,
        message: 'Media sent successfully'
      });

      // Clear media state after sending
      dispatch(clearMediaState());
    }
  }, [uploadedMedia, dispatch]);

  // Watch for media upload errors
  useEffect(() => {
    if (mediaError) {
      setSendStatus({
        success: false,
        message: mediaError
      });
    }
  }, [mediaError]);

  // Clear send status after 3 seconds
  useEffect(() => {
    if (sendStatus) {
      const timer = setTimeout(() => {
        setSendStatus(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [sendStatus]);

  const handleSubmit = () => {
    if (!message.trim() || !receiverUsername || !connected) {
      if (!connected) {
        setSendStatus({
          success: false,
          message: 'Not connected to server'
        });
      }
      return;
    }

    // Send as a text message type
    dispatch(sendMessageRequest({
      receiverUsername,
      messageText: message.trim(),
      messageType: 'text'
    }));
    setMessage('');

    // Focus back on the input after sending
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleImageSelected = async (imageFile: ImageFile) => {
    if (!receiverUsername || !connected) {
      Alert.alert('Error', 'Cannot send media: not connected');
      return;
    }

    try {
      setSendStatus({
        success: false,
        message: 'Uploading media...'
      });

      // Create file object for upload
      const file = {
        uri: imageFile.uri,
        type: imageFile.type,
        name: imageFile.name,
      };

      // Send media message directly - upload will happen in socketSaga
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: message.trim() || '', // Optional caption
        messageType: 'media',
        mediaType: imageFile.type,
        mediaFile: file, // Pass the file for upload
      }));

      setMessage(''); // Clear caption

    } catch (error) {
      console.error('Media upload error:', error);
      setSendStatus({
        success: false,
        message: 'Failed to send media'
      });
    }
  };

  // Typing indicator state
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Send typing indicator
  const sendTypingIndicator = (typing: boolean) => {
    if (connected && receiverUsername) {
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: typing ? 'typing' : 'stopped_typing',
        messageType: 'typing'
      }));
    }
  };

  // Handle text input change and typing indicator
  const handleTextChange = (text: string) => {
    setMessage(text);

    // Handle typing indicator
    if (text.trim() && !isTyping) {
      // User started typing
      setIsTyping(true);
      sendTypingIndicator(true);
    } else if (!text.trim() && isTyping) {
      // User stopped typing
      setIsTyping(false);
      sendTypingIndicator(false);
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a timeout to stop the typing indicator after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        sendTypingIndicator(false);
      }
    }, 3000);
  };

  // Clean up typing indicator on unmount
  useEffect(() => {
    return () => {
      // Clear typing timeout and send stopped typing on unmount
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      if (isTyping && connected && receiverUsername) {
        sendTypingIndicator(false);
      }
    };
  }, [isTyping, connected, receiverUsername]);

  // Handle emoji click
  const handleEmojiClick = (emoji: string) => {
    // Currently empty in frontend, could be used for inserting emoji into text
  };

  // Handle emoji long press
  const handleEmojiLongPress = (emoji: string) => {
    if (connected && receiverUsername) {
      // Get the mood name from the emoji
      let mood = '';
      switch (emoji) {
        case '😊': mood = 'happy'; break;
        case '😂': mood = 'laughing'; break;
        case '😡': mood = 'angry'; break;
        case '😢': mood = 'sad'; break;
        case '❤️': mood = 'love'; break;
        case '👍': mood = 'thumbsUp'; break;
        default: mood = 'feeling';
      }

      // Send emoji reaction message with emoji and mood
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: `${emoji}:${mood}`,
        messageType: 'emoji_reaction'
      }));
    }
  };

  // Handle emoji release
  const handleEmojiRelease = () => {
    if (connected && receiverUsername) {
      // Send message to stop showing emoji reaction
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'stopped_reaction',
        messageType: 'emoji_reaction'
      }));
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      {sendStatus && (
        <View style={[
          styles.sendStatus,
          sendStatus.success ? styles.successStatus : styles.errorStatus
        ]}>
          <Text style={styles.statusText}>{sendStatus.message}</Text>
        </View>
      )}

      {/* Upload Progress */}
      {isUploadingMedia && (
        <View style={styles.uploadProgress}>
          <Text style={styles.uploadProgressText}>
            Uploading... {uploadProgress}%
          </Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${uploadProgress}%` }]} />
          </View>
        </View>
      )}

      {/* Emoji Bar */}
      {emojiReactionsEnabled && (
        <EmojiBar
          onEmojiClick={handleEmojiClick}
          onEmojiLongPress={handleEmojiLongPress}
          onEmojiRelease={handleEmojiRelease}
        />
      )}

      <View style={styles.inputContainer}>
        <TouchableOpacity
          style={[
            styles.attachButton,
            (!connected || !receiverUsername) && styles.disabledButton
          ]}
          onPress={() => setShowMediaPicker(true)}
          disabled={!connected || !receiverUsername || isUploadingMedia}
        >
          {isUploadingMedia ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <Icon name="attach-file" style={styles.attachButtonText} />
          )}
        </TouchableOpacity>

        <TextInput
          ref={inputRef}
          style={styles.textInput}
          value={message}
          onChangeText={handleTextChange}
          placeholder="Type a message..."
          placeholderTextColor={colors.textSecondary}
          multiline
          maxLength={1000}
          onFocus={onFocus}
          onBlur={onBlur}
          editable={!isUploadingMedia}
        />

        <TouchableOpacity
          style={[
            styles.sendButton,
            (!message.trim() || !connected || !receiverUsername || isUploadingMedia) && styles.disabledButton
          ]}
          onPress={handleSubmit}
          disabled={!message.trim() || !connected || !receiverUsername || isUploadingMedia}
        >
          <Icon name="send" style={styles.sendButtonText} />
        </TouchableOpacity>
      </View>

      {/* Media Picker Modal */}
      <MediaPicker
        visible={showMediaPicker}
        onImageSelected={handleImageSelected}
        onClose={() => setShowMediaPicker(false)}
      />
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.background,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  sendStatus: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: radius.sm,
    marginBottom: spacing.sm,
  },
  successStatus: {
    backgroundColor: colors.success + '20',
    borderColor: colors.success,
    borderWidth: 1,
  },
  errorStatus: {
    backgroundColor: colors.danger + '20',
    borderColor: colors.danger,
    borderWidth: 1,
  },
  statusText: {
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    textAlign: 'center',
  },
  uploadProgress: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginBottom: spacing.sm,
  },
  uploadProgressText: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.border,
    borderRadius: radius.sm,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: radius.sm,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: spacing.sm,
  },
  attachButton: {
    backgroundColor: colors.primary,
    borderRadius: radius.round,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  attachButtonText: {
    color: colors.white,
    fontSize: 20,
  },
  textInput: {
    flex: 1,
    backgroundColor: colors.cardBackground,
    borderRadius: radius.lg,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: 16,
    fontFamily: 'Outfit-Regular',
    color: colors.text,
    maxHeight: 100,
    minHeight: 40,
  },
  sendButton: {
    backgroundColor: colors.primary,
    borderRadius: radius.round,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonText: {
    color: colors.white,
    fontSize: 20,
  },
});

export default MessageInput;
